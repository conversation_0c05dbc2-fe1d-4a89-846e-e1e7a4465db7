import React, { useRef, useEffect, useState, Suspense, useMemo } from 'react';
import { gsap } from 'gsap';

function ThreeScene() {
  const canvasRef = useRef();
  const [hasError, setHasError] = useState(false);
  const [ThreeComponents, setThreeComponents] = useState(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Intersection Observer for performance optimization
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (canvasRef.current) {
      observer.observe(canvasRef.current);
    }

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    if (!isVisible) return;

    // Lazy load Three.js components only when visible
    const loadThreeComponents = async () => {
      try {
        const { Canvas } = await import('@react-three/fiber');
        const { OrbitControls, Sphere, MeshDistortMaterial, Float } = await import('@react-three/drei');

        setThreeComponents({ Canvas, OrbitControls, Sphere, MeshDistortMaterial, Float });
      } catch (error) {
        console.warn('Three.js components failed to load:', error);
        setHasError(true);
      }
    };

    loadThreeComponents();

    // Animate canvas entrance only when components are loaded
    if (canvasRef.current && ThreeComponents) {
      gsap.fromTo(canvasRef.current,
        { opacity: 0, scale: 0.8 },
        {
          opacity: 1,
          scale: 1,
          duration: 1.5,
          ease: "power2.out",
          delay: 0.5
        }
      );
    }
  }, [isVisible, ThreeComponents]);

  // Loading state
  if (!isVisible) {
    return (
      <div
        ref={canvasRef}
        style={{
          width: '100%',
          height: '400px',
          margin: '2rem 0',
          borderRadius: 'var(--radius-xl)',
          overflow: 'hidden',
          border: '1px solid rgba(0, 212, 170, 0.3)',
          background: 'rgba(11, 20, 38, 0.8)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <div style={{
          color: 'var(--cyber-green)',
          fontSize: 'var(--text-lg)',
          textAlign: 'center'
        }}>
          <i className="fas fa-cube" style={{ fontSize: '3rem', marginBottom: '1rem', display: 'block' }}></i>
          <div>Loading 3D Visualization...</div>
        </div>
      </div>
    );
  }

  // Fallback component when Three.js fails to load
  if (hasError || !ThreeComponents) {
    return (
      <div
        ref={canvasRef}
        className="three-fallback"
      >
        <div style={{ textAlign: 'center' }}>
          <i className="fas fa-shield-alt" style={{ fontSize: '4rem', marginBottom: '1rem', color: 'var(--cyber-green)' }}></i>
          <div style={{ fontSize: 'var(--text-xl)', fontWeight: 'var(--font-semibold)', marginBottom: '0.5rem' }}>
            Cybersecurity Innovation Hub
          </div>
          <div style={{ fontSize: 'var(--text-base)', opacity: 0.8 }}>
            Advanced Security Visualization
          </div>
        </div>
      </div>
    );
  }

  // Optimized Three.js scene component
  const { Canvas, OrbitControls, Sphere, MeshDistortMaterial, Float } = ThreeComponents;

  // Memoized sphere component for better performance
  const AnimatedSphere = React.memo(({ position, color }) => {
    const meshRef = useRef();

    return (
      <Float speed={1.5} rotationIntensity={0.8} floatIntensity={1.5}>
        <Sphere ref={meshRef} position={position} args={[0.8, 16, 16]}>
          <MeshDistortMaterial
            color={color}
            attach="material"
            distort={0.2}
            speed={1}
            roughness={0.3}
            metalness={0.7}
          />
        </Sphere>
      </Float>
    );
  });

  // Memoized scene content
  const SceneContent = useMemo(() => (
    <>
      <ambientLight intensity={0.3} />
      <pointLight position={[8, 8, 8]} intensity={0.8} color="var(--cyber-green)" />
      <pointLight position={[-8, -8, -8]} intensity={0.4} color="var(--security-orange)" />

      <AnimatedSphere position={[-1.5, 0, 0]} color="var(--cyber-green)" />
      <AnimatedSphere position={[1.5, 0, 0]} color="var(--security-orange)" />
      <AnimatedSphere position={[0, -1, 0]} color="var(--white)" />

      <OrbitControls
        enableZoom={false}
        enablePan={false}
        autoRotate
        autoRotateSpeed={0.3}
        maxPolarAngle={Math.PI / 1.5}
        minPolarAngle={Math.PI / 3}
      />
    </>
  ), []);

  return (
    <div
      ref={canvasRef}
      className="three-scene-container"
    >
      <Suspense fallback={
        <div className="three-loading">
          <i className="fas fa-spinner fa-spin" style={{ fontSize: '2rem', color: 'var(--cyber-green)' }}></i>
          <div>Loading 3D Scene...</div>
        </div>
      }>
        <Canvas
          camera={{ position: [0, 0, 5], fov: 50 }}
          style={{ background: 'transparent' }}
          dpr={Math.min(window.devicePixelRatio, 2)}
          performance={{ min: 0.5 }}
          frameloop="demand"
        >
          {SceneContent}
        </Canvas>
      </Suspense>
    </div>
  );
}

export default ThreeScene;

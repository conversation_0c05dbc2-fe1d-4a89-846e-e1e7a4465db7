import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';

function LoadingScreen({ onLoadingComplete }) {
  const screenRef = useRef();
  const logoRef = useRef();
  const progressRef = useRef();
  const matrixRef = useRef();
  const terminalRef = useRef();
  const [progress, setProgress] = useState(0);
  const [loadingText, setLoadingText] = useState('INITIALIZING...');
  const [isCompleted, setIsCompleted] = useState(false);

  useEffect(() => {
    const tl = gsap.timeline();

    // Create matrix rain effect
    createMatrixRain();

    // Create cyber grid
    createCyberGrid();

    // Animate screen entrance
    tl.fromTo(screenRef.current,
      { opacity: 0 },
      { opacity: 1, duration: 0.5 }
    );

    // Animate logo
    tl.fromTo(logoRef.current,
      { scale: 0, rotationY: 180 },
      {
        scale: 1,
        rotationY: 0,
        duration: 1.2,
        ease: "back.out(1.7)"
      },
      0.3
    );

    // Start progress simulation
    simulateLoading();

    // Terminal typing effect
    startTerminalEffect();

    return () => {
      tl.kill();
    };
  }, []);

  const createMatrixRain = () => {
    const matrixContainer = matrixRef.current;
    const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';

    for (let i = 0; i < 50; i++) {
      const column = document.createElement('div');
      column.className = 'matrix-column';
      column.style.left = Math.random() * 100 + '%';
      column.style.animationDelay = Math.random() * 2 + 's';
      column.style.animationDuration = (2 + Math.random() * 3) + 's';

      for (let j = 0; j < 20; j++) {
        const char = document.createElement('span');
        char.textContent = chars[Math.floor(Math.random() * chars.length)];
        char.style.opacity = Math.random();
        column.appendChild(char);
      }

      matrixContainer.appendChild(column);
    }
  };

  const createCyberGrid = () => {
    const grid = document.createElement('div');
    grid.className = 'cyber-loading-grid';
    screenRef.current.appendChild(grid);
  };

  const simulateLoading = () => {
    const steps = [
      { text: 'INITIALIZING SECURITY PROTOCOLS...', duration: 500 },
      { text: 'LOADING ENCRYPTION MODULES...', duration: 400 },
      { text: 'ESTABLISHING SECURE CONNECTION...', duration: 600 },
      { text: 'AUTHENTICATING USER CREDENTIALS...', duration: 300 },
      { text: 'LOADING CYBERSECURITY FRAMEWORK...', duration: 400 },
      { text: 'FINALIZING SECURITY CHECKS...', duration: 300 }
    ];

    let currentStep = 0;
    let currentProgress = 0;

    const updateProgress = () => {
      if (currentStep < steps.length) {
        setLoadingText(steps[currentStep].text);

        const targetProgress = ((currentStep + 1) / steps.length) * 100;

        gsap.to({ progress: currentProgress }, {
          progress: targetProgress,
          duration: steps[currentStep].duration / 1000,
          ease: "power2.out",
          onUpdate: function() {
            currentProgress = this.targets()[0].progress;
            setProgress(Math.round(currentProgress));

            // Update progress bar
            if (progressRef.current) {
              gsap.set(progressRef.current, { width: currentProgress + '%' });
            }
          },
          onComplete: () => {
            currentStep++;
            if (currentStep < steps.length) {
              setTimeout(updateProgress, 100);
            } else {
              // All steps completed, wait for terminal to finish
              setTimeout(checkCompletion, 500);
            }
          }
        });
      }
    };

    setTimeout(updateProgress, 800);
  };

  const checkCompletion = () => {
    // Wait for terminal animation to complete
    setTimeout(() => {
      setLoadingText('SYSTEM READY - LAUNCHING...');
      setIsCompleted(true);

      // Smooth exit animation
      gsap.to(screenRef.current, {
        opacity: 0,
        scale: 0.95,
        duration: 0.8,
        ease: "power2.inOut",
        onComplete: () => {
          if (onLoadingComplete) {
            onLoadingComplete();
          }
        }
      });
    }, 1000); // Wait for terminal to finish
  };

  const startTerminalEffect = () => {
    const commands = [
      '> Scanning network vulnerabilities...',
      '> Initializing firewall protocols...',
      '> Loading threat detection systems...',
      '> Establishing secure channels...',
      '> DJS ISACA SYSTEM READY',
      '> LAUNCHING INTERFACE...'
    ];

    let commandIndex = 0;

    const typeCommand = () => {
      if (commandIndex < commands.length && terminalRef.current) {
        const command = commands[commandIndex];
        let charIndex = 0;

        const typeChar = () => {
          if (charIndex < command.length) {
            terminalRef.current.textContent = command.substring(0, charIndex + 1);
            charIndex++;
            setTimeout(typeChar, 50);
          } else {
            commandIndex++;
            if (commandIndex < commands.length) {
              setTimeout(typeCommand, 600);
            }
          }
        };

        typeChar();
      }
    };

    setTimeout(typeCommand, 1500);
  };

  return (
    <div className="cyber-loading-screen" ref={screenRef}>
      {/* Matrix Rain Background */}
      <div className="matrix-rain" ref={matrixRef}></div>

      {/* Cyber Grid Overlay */}
      <div className="cyber-grid-overlay"></div>

      {/* Main Loading Content */}
      <div className="loading-content">
        {/* Animated Logo */}
        <div className="cyber-logo" ref={logoRef}>
          <div className="logo-shield">
            <i className="fas fa-shield-alt"></i>
          </div>
          <div className="logo-text">
            <span className="logo-main">DJS ISACA</span>
            <span className="logo-sub">CYBERSECURITY DIVISION</span>
          </div>
        </div>

        {/* Progress Section */}
        <div className="progress-section">
          <div className="progress-bar-container">
            <div className="progress-bar" ref={progressRef}></div>
            <div className="progress-glow"></div>
          </div>
          <div className="progress-text">
            <span className="loading-status">{loadingText}</span>
            <span className="progress-percentage">{progress}%</span>
          </div>
        </div>

        {/* Terminal Output */}
        <div className="terminal-output">
          <div className="terminal-header">
            <span>SECURITY_TERMINAL_v2.1</span>
            <div className="terminal-controls">
              <span></span><span></span><span></span>
            </div>
          </div>
          <div className="terminal-body">
            <span ref={terminalRef}></span>
            <span className="cursor">_</span>
          </div>
        </div>
      </div>

      {/* Scanning Lines */}
      <div className="scan-lines"></div>

      {/* Corner Brackets */}
      <div className="corner-brackets">
        <div className="bracket top-left"></div>
        <div className="bracket top-right"></div>
        <div className="bracket bottom-left"></div>
        <div className="bracket bottom-right"></div>
      </div>
    </div>
  );
}

export default LoadingScreen;

import React, { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

function TestimonialsSection() {
  const sectionRef = useRef();
  const cardsRef = useRef();

  useEffect(() => {
    const cards = cardsRef.current.children;
    gsap.fromTo(cards,
      { opacity: 0, y: 50, scale: 0.9 },
      {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 0.8,
        stagger: 0.2,
        ease: "power2.out",
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      }
    );
  }, []);

  const testimonials = [
    {
      name: "<PERSON><PERSON><PERSON>",
      role: "Final Year, Computer Engineering",
      image: "👨‍💻",
      quote: "DJS ISACA completely changed my perspective on cybersecurity. The hands-on CTF competitions and real-world penetration testing workshops gave me practical skills I never thought I could learn. The mentorship from seniors and industry professionals is invaluable.",
      achievement: "CTF Competition Winner & Security Enthusiast"
    },
    {
      name: "<PERSON><PERSON>",
      role: "Third Year, Information Technology",
      image: "👩‍💻",
      quote: "I joined as a complete beginner, but the supportive community and structured learning path helped me discover my passion for ethical hacking. The workshops and study groups made complex security concepts easy to understand!",
      achievement: "Ethical Hacking Certification & Bug Bounty Participant"
    },
    {
      name: "Rohit Desai",
      role: "Final Year, Computer Science",
      image: "🛡️",
      quote: "The networking opportunities at DJS ISACA are incredible. I've learned from industry experts during guest lectures and built lasting friendships with fellow cybersecurity enthusiasts. The chapter creates a perfect learning environment.",
      achievement: "Cybersecurity Research Project Leader"
    },
    {
      name: "Sneha Kulkarni",
      role: "Second Year, Electronics Engineering",
      image: "🔬",
      quote: "What I love most about DJS ISACA is how it welcomes students from all branches. Even as an Electronics student, I found my niche in IoT security. The interdisciplinary approach here makes cybersecurity education accessible to everyone.",
      achievement: "IoT Security Workshop Organizer"
    }
  ];

  return (
    <section className="testimonials-section" ref={sectionRef}>
      <div className="testimonials-container">
        <div className="section-header">
          <h2>Student Success Stories</h2>
          <p>Real experiences from our community members who've discovered their passion for cybersecurity through DJS ISACA</p>
        </div>
        
        <div className="testimonials-grid" ref={cardsRef}>
          {testimonials.map((testimonial, index) => (
            <div key={index} className="testimonial-card">
              <div className="testimonial-header">
                <div className="student-avatar">{testimonial.image}</div>
                <div className="student-info">
                  <h4>{testimonial.name}</h4>
                  <p className="student-role">{testimonial.role}</p>
                </div>
              </div>
              
              <blockquote className="testimonial-quote">
                "{testimonial.quote}"
              </blockquote>
              
              <div className="achievement-badge">
                <i className="fas fa-trophy"></i>
                <span>{testimonial.achievement}</span>
              </div>
            </div>
          ))}
        </div>

        <div className="join-cta">
          <h3>Ready to Start Your Learning Journey?</h3>
          <p>Join hundreds of students who've discovered their passion for cybersecurity with DJS ISACA</p>
          <button className="join-button">
            <i className="fas fa-rocket"></i>
            <span>Join Our Community</span>
          </button>
        </div>
      </div>
    </section>
  );
}

export default TestimonialsSection;

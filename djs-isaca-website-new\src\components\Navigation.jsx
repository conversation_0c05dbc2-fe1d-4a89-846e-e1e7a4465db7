import React, { useState, useEffect, useRef } from 'react';
import { gsap } from 'gsap';

function Navigation() {
  const [isOpen, setIsOpen] = useState(false);
  const navRef = useRef();
  const menuRef = useRef();
  const overlayRef = useRef();

  useEffect(() => {
    // Initial animation
    gsap.fromTo(navRef.current,
      { y: -100, opacity: 0 },
      { 
        y: 0, 
        opacity: 1, 
        duration: 1, 
        ease: "power2.out",
        delay: 2.5 // After loading screen
      }
    );
  }, []);

  useEffect(() => {
    if (isOpen) {
      // Open menu animation
      gsap.set(overlayRef.current, { display: 'block' });
      gsap.to(overlayRef.current, { opacity: 1, duration: 0.3 });
      gsap.fromTo(menuRef.current,
        { x: '100%' },
        { x: '0%', duration: 0.4, ease: "power2.out" }
      );
    } else {
      // Close menu animation
      gsap.to(menuRef.current, { 
        x: '100%', 
        duration: 0.3, 
        ease: "power2.in",
        onComplete: () => {
          gsap.to(overlayRef.current, { 
            opacity: 0, 
            duration: 0.2,
            onComplete: () => {
              gsap.set(overlayRef.current, { display: 'none' });
            }
          });
        }
      });
    }
  }, [isOpen]);

  const scrollToSection = (sectionId) => {
    const element = document.querySelector(sectionId);
    if (element) {
      const navHeight = 80; // Account for fixed navigation
      const elementPosition = element.offsetTop - navHeight;

      window.scrollTo({
        top: elementPosition,
        behavior: 'smooth'
      });

      setIsOpen(false);
    }
  };

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'Escape' && isOpen) {
        setIsOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen]);

  return (
    <>
      <nav className="navigation" ref={navRef}>
        <div className="nav-container">
          <div className="nav-logo">
            <span>DJS ISACA</span>
          </div>
          
          {/* Desktop Menu */}
          <div className="nav-menu desktop-menu">
            <a href="#home" onClick={(e) => { e.preventDefault(); scrollToSection('#home'); }}>Home</a>
            <a href="#about" onClick={(e) => { e.preventDefault(); scrollToSection('#about'); }}>About</a>
            <a href="#features" onClick={(e) => { e.preventDefault(); scrollToSection('#features'); }}>Features</a>
            <a href="#team" onClick={(e) => { e.preventDefault(); scrollToSection('#team'); }}>Team</a>
            <a href="#contact" onClick={(e) => { e.preventDefault(); scrollToSection('#contact'); }}>Contact</a>
          </div>

          {/* Mobile Menu Button */}
          <button
            className={`mobile-menu-btn ${isOpen ? 'active' : ''}`}
            onClick={() => setIsOpen(!isOpen)}
            aria-label="Toggle mobile menu"
            aria-expanded={isOpen}
          >
            <span></span>
            <span></span>
            <span></span>
          </button>
        </div>
      </nav>

      {/* Mobile Menu Overlay */}
      <div className="mobile-overlay" ref={overlayRef} onClick={() => setIsOpen(false)}>
        <div className="mobile-menu" ref={menuRef} onClick={(e) => e.stopPropagation()}>
          <div className="mobile-menu-header">
            <span>Menu</span>
            <button onClick={() => setIsOpen(false)}>×</button>
          </div>
          <div className="mobile-menu-items">
            <a href="#home" onClick={(e) => { e.preventDefault(); scrollToSection('#home'); }}>Home</a>
            <a href="#about" onClick={(e) => { e.preventDefault(); scrollToSection('#about'); }}>About</a>
            <a href="#features" onClick={(e) => { e.preventDefault(); scrollToSection('#features'); }}>Features</a>
            <a href="#team" onClick={(e) => { e.preventDefault(); scrollToSection('#team'); }}>Team</a>
            <a href="#contact" onClick={(e) => { e.preventDefault(); scrollToSection('#contact'); }}>Contact</a>
          </div>
        </div>
      </div>
    </>
  );
}

export default Navigation;

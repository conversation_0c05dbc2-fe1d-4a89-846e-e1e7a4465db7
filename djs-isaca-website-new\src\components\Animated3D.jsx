import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

function Animated3D() {
  const cubeRef = useRef();
  const containerRef = useRef();
  const particlesRef = useRef();

  useEffect(() => {
    // Main cube rotation animation
    const rotationTween = gsap.to(cubeRef.current, {
      rotateY: 360,
      duration: 8,
      repeat: -1,
      ease: "none"
    });

    // Floating animation
    gsap.to(cubeRef.current, {
      y: -10,
      duration: 2,
      repeat: -1,
      yoyo: true,
      ease: "power2.inOut"
    });

    // Create floating particles
    createParticles();

    // Mouse interaction
    const handleMouseMove = (e) => {
      if (!containerRef.current) return;

      const rect = containerRef.current.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;

      const deltaX = (e.clientX - centerX) / rect.width;
      const deltaY = (e.clientY - centerY) / rect.height;

      gsap.to(cubeRef.current, {
        rotateX: -30 + deltaY * 20,
        rotateZ: deltaX * 10,
        duration: 0.5,
        ease: "power2.out"
      });
    };

    const handleMouseLeave = () => {
      gsap.to(cubeRef.current, {
        rotateX: -30,
        rotateZ: 0,
        duration: 1,
        ease: "power2.out"
      });
    };

    if (containerRef.current) {
      containerRef.current.addEventListener('mousemove', handleMouseMove);
      containerRef.current.addEventListener('mouseleave', handleMouseLeave);
    }

    return () => {
      rotationTween.kill();
      if (containerRef.current) {
        containerRef.current.removeEventListener('mousemove', handleMouseMove);
        containerRef.current.removeEventListener('mouseleave', handleMouseLeave);
      }
    };
  }, []);

  const createParticles = () => {
    if (!particlesRef.current) return;

    for (let i = 0; i < 20; i++) {
      const particle = document.createElement('div');
      particle.className = 'particle';
      particle.style.left = Math.random() * 100 + '%';
      particle.style.animationDelay = Math.random() * 6 + 's';
      particle.style.animationDuration = (4 + Math.random() * 4) + 's';
      particlesRef.current.appendChild(particle);
    }
  };

  const handleCubeHover = () => {
    gsap.to(cubeRef.current, {
      scale: 1.1,
      duration: 0.3,
      ease: "back.out(1.7)"
    });
  };

  const handleCubeLeave = () => {
    gsap.to(cubeRef.current, {
      scale: 1,
      duration: 0.3,
      ease: "back.out(1.7)"
    });
  };

  const handleCubeClick = () => {
    gsap.to(cubeRef.current, {
      rotateY: "+=360",
      duration: 1,
      ease: "power2.inOut"
    });
  };

  return (
    <>
      <div className="particles" ref={particlesRef}></div>
      <div
        className="cube-container"
        ref={containerRef}
        onMouseEnter={handleCubeHover}
        onMouseLeave={handleCubeLeave}
        onClick={handleCubeClick}
      >
        <div className="cube" ref={cubeRef}>
          <div className="face front"><i className="fas fa-shield-alt"></i></div>
          <div className="face back"><i className="fas fa-lock"></i></div>
          <div className="face right"><i className="fas fa-laptop-code"></i></div>
          <div className="face left"><i className="fas fa-network-wired"></i></div>
          <div className="face top"><i className="fas fa-user-shield"></i></div>
          <div className="face bottom"><i className="fas fa-search"></i></div>
        </div>
      </div>
    </>
  );
}

export default Animated3D;

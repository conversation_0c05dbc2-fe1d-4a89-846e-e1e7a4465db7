import React, { useRef, useEffect } from 'react';
import { gsap } from 'gsap';

function HeroSection() {
  const heroRef = useRef();
  const statsRef = useRef();

  useEffect(() => {
    // Wait for content to be ready, then animate
    const timer = setTimeout(() => {
      if (heroRef.current) {
        const tl = gsap.timeline();

        // Ensure elements exist before animating
        const heroContent = heroRef.current.querySelector('.hero-content');
        const heroCta = heroRef.current.querySelector('.hero-cta');

        if (heroContent) {
          tl.fromTo(heroContent,
            { opacity: 0, y: 50 },
            { opacity: 1, y: 0, duration: 1, ease: "power2.out" }
          );
        }

        if (heroCta) {
          tl.fromTo(heroCta,
            { opacity: 0, y: 30 },
            { opacity: 1, y: 0, duration: 0.8, ease: "power2.out" },
            "-=0.3"
          );
        }

        if (statsRef.current && statsRef.current.children.length > 0) {
          tl.fromTo(statsRef.current.children,
            { opacity: 0, y: 20 },
            { opacity: 1, y: 0, duration: 0.6, stagger: 0.1, ease: "power2.out" },
            "-=0.5"
          );
        }
      }
    }, 1000); // Delay to ensure loading is complete

    return () => clearTimeout(timer);
  }, []);

  const stats = [
    { number: "200+", label: "Active Members" },
    { number: "50+", label: "Workshops Conducted" },
    { number: "15+", label: "CTF Competitions" },
    { number: "95%", label: "Placement Rate" }
  ];

  return (
    <section id="home" className="hero-section" ref={heroRef}>
      <div className="hero-background">
        <div className="cyber-grid"></div>
        <div className="floating-icons">
          <i className="fas fa-shield-alt"></i>
          <i className="fas fa-lock"></i>
          <i className="fas fa-code"></i>
          <i className="fas fa-network-wired"></i>
          <i className="fas fa-bug"></i>
          <i className="fas fa-key"></i>
        </div>
      </div>
      
      <div className="hero-content">
        <div className="hero-badge">
          <i className="fas fa-award"></i>
          <span>ISACA Student Chapter of the Year 2023</span>
        </div>
        
        <h1>Securing Tomorrow's Digital World</h1>
        <p>Join DJSCE's most dynamic cybersecurity community where students transform into industry-ready security professionals through hands-on learning, real-world challenges, and expert mentorship.</p>
        
        <div className="hero-cta">
          <button className="cta-primary">
            <i className="fas fa-rocket"></i>
            <span>Join Our Community</span>
          </button>
          <button className="cta-secondary">
            <i className="fas fa-calendar"></i>
            <span>Upcoming Events</span>
          </button>
        </div>
      </div>

      <div className="hero-stats" ref={statsRef}>
        {stats.map((stat, index) => (
          <div key={index} className="stat-item">
            <div className="stat-number">{stat.number}</div>
            <div className="stat-label">{stat.label}</div>
          </div>
        ))}
      </div>
    </section>
  );
}

export default HeroSection;

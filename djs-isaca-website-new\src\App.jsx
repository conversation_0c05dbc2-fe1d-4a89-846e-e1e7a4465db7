import React, { useState, useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import LoadingScreen from './components/LoadingScreen';
import HeroSection from './components/HeroSection';
import Animated3D from './components/Animated3D';
import ThreeScene from './components/ThreeScene';
import Navigation from './components/Navigation';
// ScrollAnimations removed to fix loading issues
import TestimonialsSection from './components/TestimonialsSection';
import TeamSection from './components/TeamSection';
import ContactSection from './components/ContactSection';
import './styles/main.css';

function App() {
  const [loading, setLoading] = useState(true);
  const [contentReady, setContentReady] = useState(false);
  const appRef = useRef();
  const headerRef = useRef();
  const aboutRef = useRef();
  const featuresRef = useRef();
  const footerRef = useRef();

  const handleLoadingComplete = () => {
    setLoading(false);
    // Small delay to ensure DOM is ready
    setTimeout(() => {
      setContentReady(true);
    }, 100);
  };

  useEffect(() => {
    if (contentReady && !loading) {
      // Ensure all content is immediately visible without animations
      gsap.set(appRef.current, { opacity: 1 });

      // Force all sections to be visible immediately
      if (aboutRef.current) {
        gsap.set(aboutRef.current, {
          opacity: 1,
          y: 0,
          visibility: 'visible',
          display: 'block'
        });
        // Fallback: set CSS directly in case GSAP fails
        aboutRef.current.style.opacity = '1';
        aboutRef.current.style.visibility = 'visible';
        aboutRef.current.style.display = 'block';
        aboutRef.current.style.transform = 'translateY(0)';
      }
      if (featuresRef.current) {
        gsap.set(featuresRef.current, {
          opacity: 1,
          y: 0,
          visibility: 'visible',
          display: 'block'
        });
        // Fallback: set CSS directly in case GSAP fails
        featuresRef.current.style.opacity = '1';
        featuresRef.current.style.visibility = 'visible';
        featuresRef.current.style.display = 'block';
        featuresRef.current.style.transform = 'translateY(0)';

        // Make feature cards immediately visible too
        const featureCards = featuresRef.current.querySelectorAll('.feature-card');
        if (featureCards.length > 0) {
          gsap.set(featureCards, { opacity: 1, y: 0, scale: 1 });
        }
      }
      if (footerRef.current) {
        gsap.set(footerRef.current, {
          opacity: 1,
          y: 0,
          visibility: 'visible',
          display: 'block'
        });
        // Fallback: set CSS directly in case GSAP fails
        footerRef.current.style.opacity = '1';
        footerRef.current.style.visibility = 'visible';
        footerRef.current.style.display = 'block';
        footerRef.current.style.transform = 'translateY(0)';
      }
    }
  }, [contentReady, loading]);

  return (
    <div className="app-bg">
      {loading ? (
        <LoadingScreen onLoadingComplete={handleLoadingComplete} />
      ) : (
        <div ref={appRef} style={{ opacity: 1 }}>
          <Navigation />
          {/* ScrollAnimations removed for immediate loading */}
          <HeroSection />
          <Animated3D />
          <ThreeScene />

          <section id="about" className="about" ref={aboutRef}>
            <h2>About DJS ISACA</h2>
            <p>
              Founded by passionate cybersecurity enthusiasts at DJSCE, we're more than just a student chapter—we're a community of learners and security enthusiasts. Our mission is to bridge the gap between academic learning and real-world cybersecurity challenges, helping students develop practical skills in this critical field through hands-on experience, industry insights, and cutting-edge training.
            </p>
          </section>

          <section id="features" className="features" ref={featuresRef}>
            <h2>Our Impact Areas</h2>
            <div className="feature-grid">
              <div className="feature-card">
                <div className="feature-icon"><i className="fas fa-shield-alt"></i></div>
                <h3>Hands-On Security Training</h3>
                <p>Interactive workshops covering penetration testing, vulnerability assessment, digital forensics, and the latest security tools used by industry professionals.</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon"><i className="fas fa-flag"></i></div>
                <h3>Competitive CTF Events</h3>
                <p>Regular Capture The Flag competitions that challenge students with real-world security scenarios, from web exploitation to cryptography puzzles.</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon"><i className="fas fa-users"></i></div>
                <h3>Industry Networking</h3>
                <p>Direct connections with cybersecurity professionals, guest lectures from industry experts, and mentorship opportunities to learn from experienced practitioners.</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon"><i className="fas fa-certificate"></i></div>
                <h3>Professional Certifications</h3>
                <p>Guidance and preparation for industry-standard certifications like CEH, CISSP, and CompTIA Security+, helping students validate their cybersecurity knowledge.</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon"><i className="fas fa-code"></i></div>
                <h3>Secure Development</h3>
                <p>Training in secure coding practices, application security testing, and DevSecOps methodologies essential for modern software development.</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon"><i className="fas fa-brain"></i></div>
                <h3>Research & Innovation</h3>
                <p>Collaborative research projects in emerging security technologies like AI security, IoT protection, and blockchain security solutions.</p>
              </div>
            </div>
          </section>

          <TestimonialsSection />
          <TeamSection />
          <ContactSection />

          <footer className="footer" ref={footerRef}>
            <p>&copy; 2024 DJS ISACA Student Chapter | Building Tomorrow's Cybersecurity Leaders</p>
          </footer>
        </div>
      )}
    </div>
  );
}

export default App;
